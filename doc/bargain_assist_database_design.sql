-- 好友助力砍价功能数据库设计

-- 1. 助力活动配置表
CREATE TABLE `bargain_activity_config` (
  `id` bigint NOT NULL COMMENT '主键ID，雪花ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `app_id` varchar(50) NULL COMMENT '应用ID',
  `product_id` varchar(50) NOT NULL COMMENT '商品ID',
  `original_price` int NOT NULL COMMENT '商品原价(分)',
  `floor_price` int NOT NULL COMMENT '底价金额(分)',
  `min_assist_count` int NOT NULL COMMENT '最少助力人数阈值',
  `max_assist_count` int DEFAULT 50 COMMENT '最大助力人数限制',
  `activity_duration_hours` int DEFAULT 24 COMMENT '活动持续时间(小时)',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='助力活动配置表';

-- 2. 用户助力活动表
CREATE TABLE `user_bargain_activity` (
  `id` bigint NOT NULL COMMENT '主键ID，雪花ID',
  `activity_id` bigint NOT NULL COMMENT '活动配置ID',
  `user_id` varchar(50) NOT NULL COMMENT '发起用户ID',
  `app_id` varchar(50) NULL COMMENT '应用ID',
  `product_id` varchar(50) NOT NULL COMMENT '商品ID',
  `original_price` int NOT NULL COMMENT '商品原价(分)',
  `floor_price` int NOT NULL COMMENT '底价金额(分)',
  `current_price` int NOT NULL COMMENT '当前价格(分)',
  `total_bargain_amount` int DEFAULT 0 COMMENT '已砍价总金额(分)',
  `assist_count` int DEFAULT 0 COMMENT '当前助力人数',
  `min_assist_count` int NOT NULL COMMENT '最少助力人数阈值',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-进行中，2-成功(可购买)，3-失败，4-已购买',
  `expire_time` datetime NOT NULL COMMENT '活动过期时间',
  `success_time` datetime DEFAULT NULL COMMENT '成功解锁时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='用户助力活动表';

-- 3. 助力记录表
CREATE TABLE `bargain_assist_record` (
  `id` bigint NOT NULL COMMENT '主键ID，雪花ID',
  `user_bargain_id` bigint NOT NULL COMMENT '用户助力活动ID',
  `assist_user_id` varchar(50) NOT NULL COMMENT '助力用户ID',
  `initiator_user_id` varchar(50) NOT NULL COMMENT '发起用户ID',
  `bargain_amount` int NOT NULL COMMENT '本次砍价金额(分)',
  `assist_order` int NOT NULL COMMENT '助力顺序(第几个助力)',
  `app_id` varchar(50) NULL COMMENT '应用ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='助力记录表';

-- 4. 用户每日助力限制表
CREATE TABLE `user_daily_assist_limit` (
  `id` bigint NOT NULL COMMENT '主键ID，雪花ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `assist_date` date NOT NULL COMMENT '助力日期',
  `assist_count` int DEFAULT 0 COMMENT '当日助力次数',
  `app_id` varchar(50) NULL COMMENT '应用ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='用户每日助力限制表';

-- 插入示例配置数据
INSERT INTO `bargain_activity_config` (`id`, `activity_name`, `app_id`, `product_id`, `original_price`, `floor_price`, `min_assist_count`, `max_assist_count`, `activity_duration_hours`, `status`) VALUES
(1, '商品砍价活动', 'app001', 'product001', 36500, 29900, 10, 50, 24, 1);
