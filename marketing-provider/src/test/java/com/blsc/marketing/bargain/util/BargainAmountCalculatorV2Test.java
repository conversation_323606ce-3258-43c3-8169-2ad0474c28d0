package com.blsc.marketing.bargain.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 砍价金额计算器V2测试类 - 二倍均值法算法测试
 */
@DisplayName("砍价金额计算器V2测试 - 二倍均值法")
class BargainAmountCalculatorV2Test {

    @Test
    @DisplayName("基础二倍均值法测试 - 365元商品砍到299元，10人阈值")
    void testBasicDoubleMeanAlgorithm() {
        // 商品原价365元，底价299元，砍价总额66元，10人阈值
        int maxBargainAmount = 6600; // 66元 = 6600分
        int minAssistCount = 10;
        long baseSeed = 12345L;
        
        BargainAmountCalculatorV2.BargainValidationResult result = 
                BargainAmountCalculatorV2.validateBargainAlgorithm(maxBargainAmount, minAssistCount, baseSeed);
        
        // 验证算法有效性
        assertTrue(result.isValid(), "二倍均值法算法应该有效");
        assertEquals(maxBargainAmount, result.getTotalBargained(), 
                "应该精确达到最大砍价金额");
        assertEquals(1.0, result.getUtilizationRate(), 0.001,
                "砍价金额利用率应该是100%");
        
        System.out.println("二倍均值法验证结果: " + result);
        System.out.println("砍价分布: " + result.getDistribution());
    }

    @Test
    @DisplayName("二倍均值法随机性测试 - 验证不同随机种子产生不同砍价金额")
    void testDoubleMeanRandomness() {
        int maxBargainAmount = 10000; // 100元
        int minAssistCount = 5;
        int alreadyBargainAmount = 0;
        int assistOrder = 1; // 第1次助力
        
        // 使用不同的随机种子
        long[] seeds = {1000L, 2000L, 3000L, 4000L, 5000L};
        int[] bargainAmounts = new int[seeds.length];
        
        System.out.println("二倍均值法不同随机种子第1次助力的砍价金额对比：");
        for (int i = 0; i < seeds.length; i++) {
            bargainAmounts[i] = BargainAmountCalculatorV2.calculateBargainAmount(
                    assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, seeds[i]);
            System.out.printf("种子%d: %d分(%.2f元)%n", seeds[i], bargainAmounts[i], bargainAmounts[i] / 100.0);
        }
        
        // 验证随机性：不同种子的砍价金额应该不完全相同
        boolean hasVariation = false;
        for (int i = 1; i < bargainAmounts.length; i++) {
            if (bargainAmounts[i] != bargainAmounts[0]) {
                hasVariation = true;
                break;
            }
        }
        assertTrue(hasVariation, "不同随机种子的砍价金额应该有差异");
        
        // 验证二倍均值法范围：第一次砍价应该在 [1, 平均值*2] 范围内
        double averageAmount = (double) maxBargainAmount / minAssistCount; // 假设需要minAssistCount次完成
        int maxFirstTime = (int) Math.floor(averageAmount * 2);
        
        for (int amount : bargainAmounts) {
            assertTrue(amount >= 1, "砍价金额应该至少为1分");
            assertTrue(amount <= maxFirstTime, 
                    String.format("第一次砍价金额%d不应超过二倍均值%d", amount, maxFirstTime));
        }
        
        System.out.println("✅ 二倍均值法随机性验证通过");
    }

    @Test
    @DisplayName("二倍均值法一致性测试 - 验证相同随机种子多次计算结果一致")
    void testDoubleMeanConsistency() {
        int maxBargainAmount = 5000; // 50元
        int minAssistCount = 8;
        int alreadyBargainAmount = 1000; // 已砍10元
        int assistOrder = 3; // 第3次助力
        long randomSeed = 9999L;
        
        // 相同参数多次计算应该得到相同结果
        int firstResult = BargainAmountCalculatorV2.calculateBargainAmount(
                assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, randomSeed);
        
        for (int i = 0; i < 10; i++) {
            int result = BargainAmountCalculatorV2.calculateBargainAmount(
                    assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, randomSeed);
            assertEquals(firstResult, result, 
                    String.format("第%d次计算结果%d应该与首次结果%d一致", i + 1, result, firstResult));
        }
        
        System.out.printf("✅ 二倍均值法一致性验证通过：相同随机种子%d第%d次助力砍价金额始终为%d分%n", 
                randomSeed, assistOrder, firstResult);
    }

    @Test
    @DisplayName("二倍均值法边界条件测试")
    void testDoubleMeanBoundaryConditions() {
        long seed = 1111L;
        
        // 测试最小值
        int bargainAmount1 = BargainAmountCalculatorV2.calculateBargainAmount(1, 100, 5, 0, seed);
        assertTrue(bargainAmount1 >= 1, "砍价金额应该至少为1分");
        
        // 测试已砍价金额等于最大值的情况
        int bargainAmount2 = BargainAmountCalculatorV2.calculateBargainAmount(1, 100, 5, 100, seed);
        assertEquals(1, bargainAmount2, "已砍价金额达到最大值时，应该返回最小砍价金额");
        
        // 测试已砍价金额超过最大值的情况
        int bargainAmount3 = BargainAmountCalculatorV2.calculateBargainAmount(1, 100, 5, 150, seed);
        assertEquals(1, bargainAmount3, "已砍价金额超过最大值时，应该返回最小砍价金额");
        
        // 测试最后一次砍价的情况
        int bargainAmount4 = BargainAmountCalculatorV2.calculateBargainAmount(10, 1000, 5, 950, seed);
        assertEquals(50, bargainAmount4, "最后一次砍价应该获得所有剩余金额");
    }

    @Test
    @DisplayName("二倍均值法实际业务场景测试 - 365元商品砍到299元")
    void testDoubleMeanRealBusinessScenario() {
        // 实际业务场景：365元商品，底价299元，需要砍价66元，10人阈值
        int originalPrice = 36500; // 365元
        int floorPrice = 29900;    // 299元
        int maxBargainAmount = originalPrice - floorPrice; // 66元 = 6600分
        int minAssistCount = 10;
        long baseSeed = 88888L;
        
        // 模拟完整的砍价过程
        int totalBargained = 0;
        int completedAtAssist = 0;
        
        System.out.println("=== 二倍均值法砍价过程模拟 ===");
        for (int i = 1; i <= minAssistCount + 10; i++) { // 最多砍20次
            long randomSeed = baseSeed + i * 1000L; // 为每次助力生成不同的随机种子
            int bargainAmount = BargainAmountCalculatorV2.calculateBargainAmount(
                    i, maxBargainAmount, minAssistCount, totalBargained, randomSeed);
            totalBargained += bargainAmount;
            
            String phase = i <= minAssistCount ? "阈值内" : "阈值后";
            System.out.printf("第%d次助力(%s): 砍价%d分(%.2f元), 累计砍价%d分(%.2f元)%n", 
                    i, phase, bargainAmount, bargainAmount / 100.0, totalBargained, totalBargained / 100.0);
            
            // 如果已经达到最大砍价金额，记录完成时的助力次数并停止
            if (totalBargained >= maxBargainAmount) {
                completedAtAssist = i;
                System.out.printf("🎉 第%d次助力后达到最大砍价金额，活动成功！解锁底价购买！%n", i);
                break;
            }
        }
        
        // 验证能够完成砍价
        assertTrue(completedAtAssist > 0, "应该能够完成砍价");
        assertTrue(completedAtAssist <= minAssistCount + 10, "砍价次数应该在合理范围内");
        
        // 验证精确达到最大砍价金额
        assertEquals(maxBargainAmount, totalBargained, 
                String.format("总砍价金额%d分应该精确等于最大值%d分", totalBargained, maxBargainAmount));
        
        // 计算最终价格
        int finalPrice = originalPrice - totalBargained;
        System.out.printf("✅ 最终价格: %d分(%.2f元), 原价: %d分(%.2f元), 砍价: %d分(%.2f元)%n",
                finalPrice, finalPrice / 100.0, originalPrice, originalPrice / 100.0, 
                totalBargained, totalBargained / 100.0);
        
        assertEquals(floorPrice, finalPrice, "最终价格应该等于底价");
    }

    @Test
    @DisplayName("二倍均值法多轮随机性测试 - 验证每轮砍价都有随机性")
    void testDoubleMeanMultiRoundRandomness() {
        int maxBargainAmount = 8000; // 80元
        int minAssistCount = 6;
        
        // 模拟5轮不同的砍价活动
        System.out.println("=== 二倍均值法多轮随机性测试 ===");
        Set<List<Integer>> allDistributions = new HashSet<>();
        
        for (int round = 1; round <= 5; round++) {
            long baseSeed = System.currentTimeMillis() + round * 10000L;
            List<Integer> distribution = BargainAmountCalculatorV2.preCalculateBargainDistribution(
                    maxBargainAmount, minAssistCount, baseSeed);
            
            allDistributions.add(distribution);
            
            System.out.printf("第%d轮砍价分布: %s%n", round, distribution);
            
            // 验证每轮都能完成砍价
            int total = distribution.stream().mapToInt(Integer::intValue).sum();
            assertEquals(maxBargainAmount, total, 
                    String.format("第%d轮总砍价金额应该等于最大值", round));
        }
        
        // 验证不同轮次的分布应该不完全相同（体现随机性）
        assertTrue(allDistributions.size() > 1, "不同轮次的砍价分布应该有差异");
        
        System.out.println("✅ 多轮随机性验证通过：每轮砍价都有不同的分布");
    }

    @Test
    @DisplayName("二倍均值法算法特性验证 - 验证符合微信红包算法特点")
    void testDoubleMeanAlgorithmCharacteristics() {
        int maxBargainAmount = 10000; // 100元
        int minAssistCount = 10;
        long baseSeed = 66666L;
        
        List<Integer> distribution = BargainAmountCalculatorV2.preCalculateBargainDistribution(
                maxBargainAmount, minAssistCount, baseSeed);
        
        System.out.println("=== 二倍均值法算法特性验证 ===");
        System.out.println("砍价分布: " + distribution);
        
        // 验证每次砍价都在合理范围内
        int totalBargained = 0;
        for (int i = 0; i < distribution.size(); i++) {
            int bargainAmount = distribution.get(i);
            totalBargained += bargainAmount;
            
            // 计算当前剩余金额和剩余次数
            int remainingAmount = maxBargainAmount - (totalBargained - bargainAmount);
            
            if (i < distribution.size() - 1) { // 不是最后一次
                // 验证砍价金额在合理范围内（这里简化验证，实际范围计算较复杂）
                assertTrue(bargainAmount >= 1, 
                        String.format("第%d次砍价金额%d应该至少为1分", i + 1, bargainAmount));
                assertTrue(bargainAmount <= remainingAmount, 
                        String.format("第%d次砍价金额%d不应超过剩余金额%d", i + 1, bargainAmount, remainingAmount));
            }
        }
        
        // 验证总金额精确等于最大砍价金额
        assertEquals(maxBargainAmount, totalBargained, "总砍价金额应该精确等于最大值");
        
        // 验证砍价次数合理
        assertTrue(distribution.size() >= minAssistCount, "砍价次数应该至少达到阈值");
        assertTrue(distribution.size() <= minAssistCount + 10, "砍价次数应该在合理范围内");
        
        System.out.printf("✅ 算法特性验证通过：总共%d次砍价，精确完成%d分砍价%n", 
                distribution.size(), totalBargained);
    }
}
