<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.bargain.mapper.BargainActivityConfigMapper">

    <resultMap id="BaseResultMap" type="com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_name" property="activityName" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="original_price" property="originalPrice" jdbcType="INTEGER"/>
        <result column="floor_price" property="floorPrice" jdbcType="INTEGER"/>
        <result column="min_assist_count" property="minAssistCount" jdbcType="INTEGER"/>
        <result column="max_assist_count" property="maxAssistCount" jdbcType="INTEGER"/>
        <result column="activity_duration_hours" property="activityDurationHours" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, activity_name, app_id, product_id, original_price, floor_price, 
        min_assist_count, max_assist_count, activity_duration_hours, status, 
        create_time, update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_activity_config
        WHERE id = #{id}
    </select>

    <select id="selectByAppAndProduct" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_activity_config
        WHERE product_id = #{productId} AND status = 1
        LIMIT 1
    </select>

    <select id="selectActiveConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_activity_config
        WHERE AND status = 1
        ORDER BY create_time DESC
    </select>

    <insert id="insertOne" parameterType="com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO">
        INSERT INTO bargain_activity_config (
            id, activity_name, product_id, original_price, floor_price,
            min_assist_count, max_assist_count, activity_duration_hours, status,
            create_time, update_time
        ) VALUES (
            #{id}, #{activityName}, #{productId}, #{originalPrice}, #{floorPrice},
            #{minAssistCount}, #{maxAssistCount}, #{activityDurationHours}, #{status},
            #{createTime}, #{updateTime}
        )
    </insert>

    <update id="updateById" parameterType="com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO">
        UPDATE bargain_activity_config
        SET activity_name = #{activityName},
            product_id = #{productId},
            original_price = #{originalPrice},
            floor_price = #{floorPrice},
            min_assist_count = #{minAssistCount},
            max_assist_count = #{maxAssistCount},
            activity_duration_hours = #{activityDurationHours},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM bargain_activity_config WHERE id = #{id}
    </delete>

</mapper>
