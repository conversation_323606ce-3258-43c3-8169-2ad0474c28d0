<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.bargain.mapper.BargainAssistRecordMapper">

    <resultMap id="BaseResultMap" type="com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_bargain_id" property="userBargainId" jdbcType="BIGINT"/>
        <result column="assist_user_id" property="assistUserId" jdbcType="VARCHAR"/>
        <result column="initiator_user_id" property="initiatorUserId" jdbcType="VARCHAR"/>
        <result column="bargain_amount" property="bargainAmount" jdbcType="INTEGER"/>
        <result column="assist_order" property="assistOrder" jdbcType="INTEGER"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_bargain_id, assist_user_id, initiator_user_id, bargain_amount,
        assist_order, app_id, create_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_assist_record
        WHERE id = #{id}
    </select>

    <select id="selectByUserBargainId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_assist_record
        WHERE user_bargain_id = #{userBargainId}
        ORDER BY assist_order ASC
    </select>

    <select id="selectByUserBargainAndAssistUser" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_assist_record
        WHERE user_bargain_id = #{userBargainId} AND assist_user_id = #{assistUserId}
        LIMIT 1
    </select>

    <select id="selectByAssistUser" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM bargain_assist_record
        WHERE assist_user_id = #{assistUserId}
        ORDER BY create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countByAssistUser" resultType="int">
        SELECT COUNT(1)
        FROM bargain_assist_record
        WHERE assist_user_id = #{assistUserId}
    </select>

    <insert id="insertOne" parameterType="com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO">
        INSERT INTO bargain_assist_record (
            id, user_bargain_id, assist_user_id, initiator_user_id, bargain_amount,
            assist_order, create_time
        ) VALUES (
            #{id}, #{userBargainId}, #{assistUserId}, #{initiatorUserId}, #{bargainAmount},
            #{assistOrder}, #{createTime}
        )
    </insert>

    <update id="updateById" parameterType="com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO">
        UPDATE bargain_assist_record
        SET user_bargain_id = #{userBargainId},
            assist_user_id = #{assistUserId},
            initiator_user_id = #{initiatorUserId},
            bargain_amount = #{bargainAmount},
            assist_order = #{assistOrder},
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM bargain_assist_record WHERE id = #{id}
    </delete>

</mapper>
