package com.blsc.marketing.bargain.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PageData
 * @Description 分页数据对象
 * <AUTHOR>
 * @Date 2024/10/30 10:49
 */
@Data
@Accessors(chain = true)
public class PageData<T> implements Serializable {
    /**
     * 当前页数
     */
    private Integer pageNum;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 记录总数
     */
    private Long totalSize;

    /**
     * 数据列表
     */
    private List<T> list;
}
