package com.blsc.marketing.bargain.mapper;

import com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户每日助力限制Mapper
 */
@Mapper
public interface UserDailyAssistLimitMapper {
    
    /**
     * 根据ID查询
     */
    UserDailyAssistLimitPO selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID、日期和应用ID查询
     */
    UserDailyAssistLimitPO selectByUserAndDate(@Param("userId") String userId, 
                                               @Param("assistDate") LocalDate assistDate);
    
    /**
     * 查询用户的助力限制记录
     */
    List<UserDailyAssistLimitPO> selectByUser(@Param("userId") String userId, 
                                              @Param("appId") String appId,
                                              @Param("offset") Integer offset, 
                                              @Param("pageSize") Integer pageSize);
    
    /**
     * 插入一条记录
     */
    int insertOne(UserDailyAssistLimitPO limit);
    
    /**
     * 根据ID更新
     */
    int updateById(UserDailyAssistLimitPO limit);
    
    /**
     * 清理过期记录（保留最近30天）
     */
    int cleanExpiredRecords(@Param("beforeDate") LocalDate beforeDate);
    
    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);
}
