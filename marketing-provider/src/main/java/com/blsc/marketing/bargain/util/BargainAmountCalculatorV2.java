package com.blsc.marketing.bargain.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 砍价金额计算器V2 - 基于二倍均值法（微信红包算法）
 * 
 * 算法说明：
 * 1. 采用微信红包的二倍均值法算法
 * 2. 每次砍价金额在 0.01元 到 剩余平均值×2 之间随机
 * 3. 动态计算：每次砍价后重新计算剩余金额和剩余次数
 * 4. 最后一次砍价获得所有剩余金额
 * 5. 确保算法的公平性和真正的随机性
 */
@Slf4j
public class BargainAmountCalculatorV2 {
    
    /**
     * 最小砍价金额(分) - 对应微信红包的0.01元
     */
    private static final int MIN_BARGAIN_AMOUNT = 1;
    
    /**
     * 计算单次砍价金额 - 基于二倍均值法
     *
     * @param currentAssistOrder 当前助力顺序(从1开始)
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 助力人数，达到该人数阈值后砍价结束
     * @param alreadyBargainAmount 已砍价总金额(分)
     * @param randomSeed 随机种子，由业务层提供，确保每次助力的随机性
     * @return 本次砍价金额(分)
     */
    public static int calculateBargainAmount(int currentAssistOrder, int maxBargainAmount,
                                           int minAssistCount, int alreadyBargainAmount, long randomSeed) {

        if (currentAssistOrder <= 0 || maxBargainAmount <= 0 || minAssistCount <= 0) {
            log.warn("砍价参数无效: currentAssistOrder={}, maxBargainAmount={}, minAssistCount={}",
                    currentAssistOrder, maxBargainAmount, minAssistCount);
            return MIN_BARGAIN_AMOUNT;
        }

        // 如果已经砍价金额超过或等于最大值，返回最小值
        if (alreadyBargainAmount >= maxBargainAmount) {
            return MIN_BARGAIN_AMOUNT;
        }

        // 计算剩余可砍价金额
        int remainingAmount = maxBargainAmount - alreadyBargainAmount;

        // 二倍均值法核心逻辑：当达到最少助力人数阈值时，最后一次获得所有剩余金额
        if (currentAssistOrder == minAssistCount) {
            log.debug("达到阈值的最后一次砍价: 第{}次助力获得所有剩余金额{}分", currentAssistOrder, remainingAmount);
            return remainingAmount;
        }

        // 计算剩余砍价次数（包括当前这次）
        int remainingCount = minAssistCount - currentAssistOrder + 1;

        // 使用业务层提供的随机种子创建随机数生成器
        Random random = new Random(randomSeed);

        // 计算剩余平均值
        double averageAmount = (double) remainingAmount / remainingCount;

        // 二倍均值法：砍价金额在 MIN_BARGAIN_AMOUNT 到 averageAmount * 2 之间随机
        int maxThisTime = (int) Math.floor(averageAmount * 2);

        // 确保不超过剩余金额，并且为后续砍价留出空间
        int reserveForFuture = (remainingCount - 1) * MIN_BARGAIN_AMOUNT;
        maxThisTime = Math.min(maxThisTime, remainingAmount - reserveForFuture);

        // 确保至少有最小砍价金额
        maxThisTime = Math.max(maxThisTime, MIN_BARGAIN_AMOUNT);

        // 在范围内随机选择砍价金额
        int bargainAmount;
        if (maxThisTime <= MIN_BARGAIN_AMOUNT) {
            bargainAmount = MIN_BARGAIN_AMOUNT;
        } else {
            // 在 [MIN_BARGAIN_AMOUNT, maxThisTime] 范围内随机
            bargainAmount = MIN_BARGAIN_AMOUNT + random.nextInt(maxThisTime - MIN_BARGAIN_AMOUNT + 1);
        }

        log.debug("二倍均值法砍价计算: 第{}次助力, 剩余金额={}, 剩余次数={}, 平均值={}, 最大本次={}, 实际砍价={}",
                currentAssistOrder, remainingAmount, remainingCount, averageAmount, maxThisTime, bargainAmount);

        return bargainAmount;
    }
    

    
    /**
     * 预计算砍价分布 - 用于验证算法
     *
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param baseSeed 基础随机种子
     * @return 每次砍价金额列表
     */
    public static List<Integer> preCalculateBargainDistribution(int maxBargainAmount, int minAssistCount, long baseSeed) {
        List<Integer> distribution = new ArrayList<>();
        int totalBargained = 0;

        for (int i = 1; i <= minAssistCount + 10; i++) { // 多计算10次，验证超过阈值的情况
            // 为每次助力生成不同的随机种子
            long randomSeed = baseSeed + i * 1000L;
            int bargainAmount = calculateBargainAmount(i, maxBargainAmount, minAssistCount, totalBargained, randomSeed);
            distribution.add(bargainAmount);
            totalBargained += bargainAmount;

            // 如果达到或超过最大砍价金额，确保精确等于最大值
            if (totalBargained >= maxBargainAmount) {
                // 调整最后一次砍价金额，确保总额精确等于最大值
                int lastIndex = distribution.size() - 1;
                int adjustment = totalBargained - maxBargainAmount;
                distribution.set(lastIndex, distribution.get(lastIndex) - adjustment);
                break;
            }
        }

        return distribution;
    }
    
    /**
     * 验证砍价算法是否满足要求
     *
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param baseSeed 基础随机种子
     * @return 验证结果
     */
    public static BargainValidationResult validateBargainAlgorithm(int maxBargainAmount, int minAssistCount, long baseSeed) {
        List<Integer> distribution = preCalculateBargainDistribution(maxBargainAmount, minAssistCount, baseSeed);

        int totalBargained = distribution.stream().mapToInt(Integer::intValue).sum();
        boolean isValid = totalBargained <= maxBargainAmount;
        double utilizationRate = (double) totalBargained / maxBargainAmount;

        return new BargainValidationResult(isValid, totalBargained, utilizationRate, distribution);
    }
    
    /**
     * 砍价验证结果
     */
    public static class BargainValidationResult {
        private final boolean valid;
        private final int totalBargained;
        private final double utilizationRate;
        private final List<Integer> distribution;
        
        public BargainValidationResult(boolean valid, int totalBargained, 
                                     double utilizationRate, List<Integer> distribution) {
            this.valid = valid;
            this.totalBargained = totalBargained;
            this.utilizationRate = utilizationRate;
            this.distribution = distribution;
        }
        
        public boolean isValid() { return valid; }
        public int getTotalBargained() { return totalBargained; }
        public double getUtilizationRate() { return utilizationRate; }
        public List<Integer> getDistribution() { return distribution; }
        
        @Override
        public String toString() {
            return String.format("BargainValidationResult{valid=%s, totalBargained=%d, utilizationRate=%.2f%%, distribution=%s}", 
                    valid, totalBargained, utilizationRate * 100, distribution);
        }
    }
}
